# RPAL Portal Backend JWT集成指南

## 概述

本项目已成功集成JWT（JSON Web Token）认证机制，提供无状态的用户认证和授权功能。

## JWT功能特性

### 1. 双Token机制
- **访问Token（Access Token）**：用于API访问认证，有效期24小时
- **刷新Token（Refresh Token）**：用于获取新的访问Token，有效期7天

### 2. 安全特性
- 使用HMAC SHA-256算法签名
- 无状态认证，不依赖Session
- Token包含用户基本信息（用户ID、用户名、邮箱）
- 支持Token类型验证，防止Token误用

### 3. 自动认证过滤器
- 自动从请求头提取和验证JWT Token
- 集成Spring Security认证体系
- 支持路径白名单配置

## API接口变更

### 1. 用户登录接口更新

**接口：** `POST /users/login`

**新的响应格式：**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 86400,
    "userInfo": {
      "id": "RPAL-A1B2C3D4E501",
      "username": "john_doe",
      "email": "<EMAIL>",
      "orcid": "0000-0000-0000-0000",
      "researchField": "Computer Science",
      "avatarUrl": null,
      "createdAt": "2024-01-01T12:00:00",
      "updatedAt": "2024-01-01T12:00:00"
    }
  },
  "timestamp": "2024-01-01T12:00:00"
}
```

### 2. 新增刷新Token接口

**接口：** `POST /users/refresh-token`

**请求体：**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**响应：**
```json
{
  "code": 200,
  "message": "Token刷新成功",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 86400,
    "userInfo": {
      // 用户信息...
    }
  }
}
```

### 3. JWT认证测试接口

新增了 `/auth-test` 路径下的测试接口：
- `GET /auth-test/current-user` - 获取当前用户信息
- `GET /auth-test/token-info` - 获取Token详细信息
- `GET /auth-test/auth-status` - 测试认证状态
- `GET /auth-test/protected-resource` - 受保护资源访问测试

## 使用方法

### 1. 前端集成示例

```javascript
// 1. 用户登录获取Token
const login = async (credentials) => {
  const response = await fetch('/users/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(credentials)
  });
  
  const result = await response.json();
  if (result.code === 200) {
    // 保存Token到localStorage
    localStorage.setItem('accessToken', result.data.accessToken);
    localStorage.setItem('refreshToken', result.data.refreshToken);
    return result.data;
  }
  throw new Error(result.message);
};

// 2. 在请求中添加认证头
const apiCall = async (url, options = {}) => {
  const token = localStorage.getItem('accessToken');
  
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers
  };
  
  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }
  
  return fetch(url, {
    ...options,
    headers
  });
};

// 3. Token刷新机制
const refreshToken = async () => {
  const refreshToken = localStorage.getItem('refreshToken');
  
  const response = await fetch('/users/refresh-token', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ refreshToken })
  });
  
  const result = await response.json();
  if (result.code === 200) {
    localStorage.setItem('accessToken', result.data.accessToken);
    localStorage.setItem('refreshToken', result.data.refreshToken);
    return result.data.accessToken;
  }
  
  // 刷新失败，清除Token并跳转到登录页
  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');
  window.location.href = '/login';
};

// 4. 自动Token刷新拦截器
const setupTokenInterceptor = () => {
  // 响应拦截器
  const originalFetch = window.fetch;
  window.fetch = async (...args) => {
    const response = await originalFetch(...args);
    
    if (response.status === 401) {
      // Token过期，尝试刷新
      try {
        await refreshToken();
        // 重新发送原请求
        const newToken = localStorage.getItem('accessToken');
        if (args[1] && args[1].headers) {
          args[1].headers.Authorization = `Bearer ${newToken}`;
        }
        return originalFetch(...args);
      } catch (error) {
        // 刷新失败，跳转登录
        window.location.href = '/login';
      }
    }
    
    return response;
  };
};
```

### 2. 移动端集成示例

```dart
// Flutter示例
class AuthService {
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  
  // 登录
  Future<AuthResponse> login(String emailOrUsername, String password) async {
    final response = await http.post(
      Uri.parse('$baseUrl/users/login'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'emailOrUsername': emailOrUsername,
        'password': password,
      }),
    );
    
    if (response.statusCode == 200) {
      final result = jsonDecode(response.body);
      if (result['code'] == 200) {
        final authData = result['data'];
        
        // 保存Token
        await _saveTokens(authData['accessToken'], authData['refreshToken']);
        
        return AuthResponse.fromJson(authData);
      }
    }
    throw Exception('登录失败');
  }
  
  // 添加认证头
  Future<Map<String, String>> getAuthHeaders() async {
    final token = await _getAccessToken();
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }
  
  // Token刷新
  Future<String?> refreshToken() async {
    final refreshToken = await _getRefreshToken();
    if (refreshToken == null) return null;
    
    final response = await http.post(
      Uri.parse('$baseUrl/users/refresh-token'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'refreshToken': refreshToken}),
    );
    
    if (response.statusCode == 200) {
      final result = jsonDecode(response.body);
      if (result['code'] == 200) {
        final authData = result['data'];
        await _saveTokens(authData['accessToken'], authData['refreshToken']);
        return authData['accessToken'];
      }
    }
    
    // 刷新失败，清除Token
    await _clearTokens();
    return null;
  }
}
```

## 配置说明

### 1. JWT配置参数

在 `application.yml` 中配置：

```yaml
jwt:
  # JWT密钥（生产环境请使用更复杂的密钥）
  secret: RPAL_Portal_Backend_JWT_Secret_Key_2024_Very_Long_Secret_Key_For_Security_Must_Be_At_Least_256_Bits
  # 访问Token过期时间（小时）
  expiration: 24
  # 刷新Token过期时间（天）
  refresh-expiration: 7
```

### 2. 路径白名单配置

在 `JwtAuthenticationFilter` 中配置不需要JWT认证的路径：

```java
String[] skipPaths = {
    "/hello/",
    "/users/send-verification-code",
    "/users/verify-code", 
    "/users/register",
    "/users/login",
    "/users/check-email",
    "/users/check-username",
    "/users/refresh-token",
    "/swagger-ui/",
    "/v3/api-docs",
    "/swagger-resources/",
    "/webjars/"
};
```

## Swagger UI测试

1. 访问 Swagger UI：`http://localhost:8081/rpal_portal/swagger-ui.html`
2. 先调用登录接口获取Token
3. 点击右上角的🔒按钮，输入 `Bearer {your-token}`
4. 现在可以测试需要认证的接口了

## 安全建议

### 1. 生产环境配置
- 使用更复杂的JWT密钥（至少256位）
- 适当调整Token过期时间
- 启用HTTPS
- 配置CORS策略

### 2. Token管理
- 前端应安全存储Token（避免XSS攻击）
- 实现自动Token刷新机制
- 在用户登出时清除Token
- 监控异常Token使用

### 3. 错误处理
- 统一处理Token过期
- 提供友好的错误提示
- 实现自动重试机制

## 故障排除

### 1. 常见问题

**Q: Token验证失败**
A: 检查Token格式是否正确，确保包含 "Bearer " 前缀

**Q: 403 Forbidden错误**
A: 检查路径是否在白名单中，或Token是否有效

**Q: Token过期太快**
A: 调整 `jwt.expiration` 配置参数

### 2. 调试技巧

- 使用 `/auth-test/token-info` 接口查看Token详情
- 检查服务器日志中的JWT相关信息
- 使用浏览器开发者工具查看请求头

## 扩展功能

后续可以基于现有JWT机制扩展：
- 角色权限控制（RBAC）
- Token黑名单机制
- 多设备登录管理
- 单点登录（SSO）
- OAuth2集成

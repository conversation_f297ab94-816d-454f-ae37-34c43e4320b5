package com.rpal.rpal_portal_backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rpal.rpal_portal_backend.entity.Category;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分类Mapper接口
 */
@Mapper
public interface CategoryMapper extends BaseMapper<Category> {

    /**
     * 获取所有二级分类（level=2）
     */
    List<Category> selectAllSubCategories();

    /**
     * 根据父级ID获取子分类
     */
    List<Category> selectByParentId(@Param("parentId") Long parentId);

    /**
     * 获取分类及其工具数量
     */
    List<Category> selectCategoriesWithToolCount();

    /**
     * 根据分类ID获取分类信息
     */
    Category selectById(@Param("id") Long id);

    /**
     * 根据分类ID获取工具数量
     */
    Integer countToolsByCategoryId(Long categoryId);

    /**
     * 获取所有分类
     */
    List<Category> selectAllCategories();
}
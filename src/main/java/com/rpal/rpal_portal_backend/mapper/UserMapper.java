package com.rpal.rpal_portal_backend.mapper;

import com.rpal.rpal_portal_backend.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户Mapper接口
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据邮箱查询用户
     * @param email 邮箱地址
     * @return 用户信息
     */
    User selectByEmail(@Param("email") String email);

    /**
     * 根据用户名查询用户
     * @param username 用户名
     * @return 用户信息
     */
    User selectByUsername(@Param("username") String username);

    /**
     * 根据邮箱或用户名查询用户
     * @param emailOrUsername 邮箱地址或用户名
     * @return 用户信息
     */
    User selectByEmailOrUsername(@Param("emailOrUsername") String emailOrUsername);

    /**
     * 检查邮箱是否已存在
     * @param email 邮箱地址
     * @return 存在返回true，否则返回false
     */
    boolean existsByEmail(@Param("email") String email);

    /**
     * 检查用户名是否已存在
     * @param username 用户名
     * @return 存在返回true，否则返回false
     */
    boolean existsByUsername(@Param("username") String username);

    /**
     * 检查ORCID是否已存在
     * @param orcid ORCID标识
     * @return 存在返回true，否则返回false
     */
    boolean existsByOrcid(@Param("orcid") String orcid);
}

package com.rpal.rpal_portal_backend.mapper;

import com.rpal.rpal_portal_backend.entity.UserAction;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;

/**
 * 用户行为Mapper接口
 */
@Mapper
public interface UserActionMapper extends BaseMapper<UserAction> {

    /**
     * 检查用户在指定日期是否已经点击过某个工具
     * @param userId 用户ID
     * @param toolId 工具ID
     * @param date 日期（YYYY-MM-DD格式）
     * @return 是否存在点击记录
     */
    boolean existsClickByUserAndToolAndDate(@Param("userId") String userId, 
                                          @Param("toolId") Long toolId, 
                                          @Param("date") LocalDate date);

    /**
     * 记录用户点击行为
     * @param userAction 用户行为
     * @return 影响行数
     */
    int insertUserAction(UserAction userAction);
}
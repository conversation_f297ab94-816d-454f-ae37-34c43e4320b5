package com.rpal.rpal_portal_backend.mapper;

import com.rpal.rpal_portal_backend.entity.Tool;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工具Mapper接口
 */
@Mapper
public interface ToolMapper extends BaseMapper<Tool> {

    /**
     * 根据分类ID查询工具列表
     * @param categoryId 分类ID
     * @return 工具列表
     */
    List<Tool> selectByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 根据状态查询工具列表
     * @param status 状态
     * @return 工具列表
     */
    List<Tool> selectByStatus(@Param("status") String status);

    /**
     * 搜索工具（根据名称和描述）
     * @param keyword 关键词
     * @return 工具列表
     */
    List<Tool> searchTools(@Param("keyword") String keyword);

    /**
     * 获取热门工具（按点击次数排序）
     * @param limit 限制数量
     * @return 工具列表
     */
    List<Tool> selectPopularTools(@Param("limit") Integer limit);

    /**
     * 增加工具点击次数
     * @param toolId 工具ID
     * @return 影响行数
     */
    int incrementClickCount(@Param("toolId") Long toolId);

    /**
     * 根据用户ID查询收藏的工具列表
     * @param userId 用户ID
     * @return 工具列表
     */
    List<Tool> selectFavoriteToolsByUserId(@Param("userId") String userId);
}

package com.rpal.rpal_portal_backend.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Swagger3配置类
 * 使用OpenAPI 3规范
 */
@Configuration
public class SwaggerConfig {

    @Value("${server.port:8081}")
    private String serverPort;

    @Value("${server.servlet.context-path:/rpal_portal}")
    private String contextPath;

    /**
     * 配置OpenAPI信息
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(apiInfo())
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort + contextPath)
                                .description("本地开发环境"),
                        new Server()
                                .url("https://api.rpal.com" + contextPath)
                                .description("生产环境")
                ))
                .components(new Components()
                        .addSecuritySchemes("Bearer Authentication",
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")
                                        .description("请输入JWT Token，格式：Bearer {token}")
                        )
                )
                .addSecurityItem(new SecurityRequirement().addList("Bearer Authentication"));
    }

    /**
     * API信息配置
     */
    private Info apiInfo() {
        return new Info()
                .title("RPAL门户网站后端API")
                .description("RPAL门户网站后端服务API文档，提供用户管理、工具管理、新闻管理等功能。" +
                        "\n\n**认证说明：**" +
                        "\n- 大部分接口需要JWT认证" +
                        "\n- 请先调用登录接口获取Token" +
                        "\n- 在请求头中添加：Authorization: Bearer {your-token}" +
                        "\n- 或使用右上角的🔒按钮进行认证")
                .version("1.0.0")
                .contact(new Contact()
                        .name("RPAL开发团队")
                        .email("<EMAIL>")
                        .url("https://www.rpal.com"))
                .license(new License()
                        .name("MIT License")
                        .url("https://opensource.org/licenses/MIT"));
    }
}

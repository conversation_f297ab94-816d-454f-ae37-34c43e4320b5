package com.rpal.rpal_portal_backend.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.rpal.rpal_portal_backend.common.enums.ResponseCode;
import com.rpal.rpal_portal_backend.common.result.Result;
import com.rpal.rpal_portal_backend.common.utils.JwtUtils;
import com.rpal.rpal_portal_backend.entity.User;
import com.rpal.rpal_portal_backend.mapper.UserMapper;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;

/**
 * JWT认证过滤器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtUtils jwtUtils;
    private final UserMapper userMapper;
    private final ObjectMapper objectMapper;

    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        try {
            // 从请求头中获取Token
            String authHeader = request.getHeader(JwtUtils.TOKEN_HEADER);
            String token = jwtUtils.extractTokenFromHeader(authHeader);

            // 如果Token存在且有效，进行认证
            if (StringUtils.hasText(token) && jwtUtils.validateToken(token)) {
                // 检查是否为访问Token
                if (!jwtUtils.isAccessToken(token)) {
                    log.warn("使用了非访问Token进行认证: {}", jwtUtils.getTokenInfo(token));
                    handleAuthenticationError(response, "无效的Token类型");
                    return;
                }

                // 从Token中获取用户信息
                String userId = jwtUtils.getUserIdFromToken(token);

                // 验证用户是否存在且未被删除
                User user = userMapper.selectById(userId);
                if (user == null) {
                    log.warn("Token中的用户不存在: {}", userId);
                    handleAuthenticationError(response, "用户不存在");
                    return;
                }

                // 如果当前没有认证信息，设置认证
                if (SecurityContextHolder.getContext().getAuthentication() == null) {
                    // 创建用户详情对象
                    UserDetails userDetails = createUserDetails(user);
                    
                    // 创建认证Token
                    UsernamePasswordAuthenticationToken authToken = 
                        new UsernamePasswordAuthenticationToken(
                            userDetails, 
                            null, 
                            userDetails.getAuthorities()
                        );
                    
                    // 设置请求详情
                    authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                    
                    // 设置到安全上下文
                    SecurityContextHolder.getContext().setAuthentication(authToken);
                    
                    log.debug("JWT认证成功，用户: {}", userId);
                }
            }
        } catch (Exception e) {
            log.error("JWT认证过程中发生错误: {}", e.getMessage(), e);
            handleAuthenticationError(response, "认证失败");
            return;
        }

        // 继续过滤器链
        filterChain.doFilter(request, response);
    }

    /**
     * 创建UserDetails对象
     */
    private UserDetails createUserDetails(User user) {
        return org.springframework.security.core.userdetails.User.builder()
                .username(user.getId()) // 使用用户ID作为用户名
                .password("") // 密码留空，因为JWT认证不需要密码
                .authorities(new ArrayList<>()) // 暂时不设置权限，后续可以扩展
                .accountExpired(false)
                .accountLocked(false)
                .credentialsExpired(false)
                .disabled(false)
                .build();
    }

    /**
     * 处理认证错误
     */
    private void handleAuthenticationError(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());

        Result<Void> result = Result.error(ResponseCode.UNAUTHORIZED.getCode(), message);
        String jsonResponse = objectMapper.writeValueAsString(result);
        
        response.getWriter().write(jsonResponse);
        response.getWriter().flush();
    }

    /**
     * 判断是否跳过JWT认证的路径
     */
    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        String path = request.getRequestURI();
        
        // 跳过的路径列表
        String[] skipPaths = {
            "/hello/",
            "/users/send-verification-code",
            "/users/verify-code", 
            "/users/register",
            "/users/login",
            "/users/check-email",
            "/users/check-username",
            "/users/refresh-token",
            "/swagger-ui/",
            "/v3/api-docs",
            "/swagger-resources/",
            "/webjars/"
        };

        // 检查是否匹配跳过的路径
        for (String skipPath : skipPaths) {
            if (path.contains(skipPath)) {
                return true;
            }
        }

        return false;
    }
}

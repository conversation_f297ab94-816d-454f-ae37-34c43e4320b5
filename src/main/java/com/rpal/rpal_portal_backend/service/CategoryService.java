package com.rpal.rpal_portal_backend.service;

import com.rpal.rpal_portal_backend.dto.response.CategoryResponse;

import java.util.List;

/**
 * 分类服务接口
 */
public interface CategoryService {

    /**
     * 获取所有二级分类
     */
    List<CategoryResponse> getAllSubCategories();

    /**
     * 根据父级ID获取子分类
     */
    List<CategoryResponse> getCategoriesByParentId(Long parentId);

    /**
     * 根据分类ID获取分类信息
     */
    CategoryResponse getCategoryById(Long categoryId);
} 
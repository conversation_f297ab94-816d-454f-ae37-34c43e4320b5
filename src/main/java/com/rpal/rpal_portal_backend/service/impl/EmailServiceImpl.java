package com.rpal.rpal_portal_backend.service.impl;

import com.rpal.rpal_portal_backend.common.exception.BusinessException;
import com.rpal.rpal_portal_backend.service.EmailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.time.Duration;

/**
 * 邮件服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmailServiceImpl implements EmailService {

    private final JavaMailSender mailSender;
    private final StringRedisTemplate redisTemplate;

    @Value("${spring.mail.username}")
    private String fromEmail;

    // Redis键前缀
    private static final String VERIFICATION_CODE_PREFIX = "verification_code:";
    private static final String SEND_TIME_PREFIX = "send_time:";
    
    // 验证码有效期（5分钟）
    private static final Duration CODE_EXPIRE_TIME = Duration.ofMinutes(5);
    
    // 发送间隔限制（60秒）
    private static final Duration SEND_INTERVAL = Duration.ofSeconds(60);

    private static final SecureRandom RANDOM = new SecureRandom();

    @Override
    public void sendVerificationCode(String email, String code) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(email);
            message.setSubject("RPAL门户网站 - 邮箱验证码");
            message.setText(buildEmailContent(code));
            
            mailSender.send(message);
            log.info("验证码邮件发送成功，邮箱: {}", email);
        } catch (Exception e) {
            log.error("验证码邮件发送失败，邮箱: {}", email, e);
            throw new BusinessException("邮件发送失败，请稍后重试");
        }
    }

    @Override
    public String generateVerificationCode() {
        return String.format("%06d", RANDOM.nextInt(1000000));
    }

    @Override
    public void storeVerificationCode(String email, String code) {
        String key = VERIFICATION_CODE_PREFIX + email;
        redisTemplate.opsForValue().set(key, code, CODE_EXPIRE_TIME);
        log.debug("验证码已存储到Redis，邮箱: {}, 有效期: {} 分钟", email, CODE_EXPIRE_TIME.toMinutes());
    }

    @Override
    public boolean verifyCode(String email, String code) {
        String key = VERIFICATION_CODE_PREFIX + email;
        String storedCode = redisTemplate.opsForValue().get(key);
        
        if (storedCode == null) {
            log.warn("验证码不存在或已过期，邮箱: {}", email);
            return false;
        }
        
        boolean isValid = storedCode.equals(code);
        log.debug("验证码验证结果，邮箱: {}, 结果: {}", email, isValid);
        
        return isValid;
    }

    @Override
    public void removeVerificationCode(String email) {
        String key = VERIFICATION_CODE_PREFIX + email;
        redisTemplate.delete(key);
        log.debug("验证码已删除，邮箱: {}", email);
    }

    @Override
    public boolean canSendCode(String email) {
        String key = SEND_TIME_PREFIX + email;
        String lastSendTime = redisTemplate.opsForValue().get(key);
        
        if (lastSendTime == null) {
            return true;
        }
        
        long lastTime = Long.parseLong(lastSendTime);
        long currentTime = System.currentTimeMillis();
        long interval = currentTime - lastTime;
        
        boolean canSend = interval >= SEND_INTERVAL.toMillis();
        log.debug("验证码发送频率检查，邮箱: {}, 可以发送: {}, 距离上次发送: {} 秒", 
                email, canSend, interval / 1000);
        
        return canSend;
    }

    @Override
    public void recordSendTime(String email) {
        String key = SEND_TIME_PREFIX + email;
        String currentTime = String.valueOf(System.currentTimeMillis());
        redisTemplate.opsForValue().set(key, currentTime, SEND_INTERVAL);
        log.debug("验证码发送时间已记录，邮箱: {}", email);
    }

    /**
     * 构建邮件内容
     */
    private String buildEmailContent(String code) {
        return String.format(
                "亲爱的用户：\n\n" +
                "您好！感谢您注册RPAL门户网站。\n\n" +
                "您的邮箱验证码是：%s\n\n" +
                "验证码有效期为5分钟，请及时使用。\n" +
                "如果您没有进行此操作，请忽略此邮件。\n\n" +
                "此邮件由系统自动发送，请勿回复。\n\n" +
                "RPAL门户网站\n" +
                "%s",
                code,
                java.time.LocalDateTime.now().format(
                        java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                )
        );
    }
}

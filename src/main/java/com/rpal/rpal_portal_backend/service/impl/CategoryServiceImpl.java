package com.rpal.rpal_portal_backend.service.impl;

import com.rpal.rpal_portal_backend.common.exception.BusinessException;
import com.rpal.rpal_portal_backend.dto.response.CategoryResponse;
import com.rpal.rpal_portal_backend.entity.Category;
import com.rpal.rpal_portal_backend.mapper.CategoryMapper;
import com.rpal.rpal_portal_backend.service.CategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;
import java.util.ArrayList;

/**
 * 分类服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CategoryServiceImpl implements CategoryService {

    private final CategoryMapper categoryMapper;

    @Override
    public List<CategoryResponse> getAllSubCategories() {
        List<Category> categories = categoryMapper.selectCategoriesWithToolCount();
        return categories.stream()
                .map(this::convertToCategoryResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<CategoryResponse> getCategoriesByParentId(Long parentId) {
        List<Category> categories = categoryMapper.selectByParentId(parentId);
        return categories.stream()
                .map(this::convertToCategoryResponse)
                .collect(Collectors.toList());
    }

    @Override
    public CategoryResponse getCategoryById(Long categoryId) {
        Category category = categoryMapper.selectById(categoryId);
        if (category == null) {
            throw new BusinessException("分类不存在");
        }
        CategoryResponse response = convertToCategoryResponse(category);
        response.setToolCount(categoryMapper.countToolsByCategoryId(categoryId));
        return response;
    }

    @Override
    public List<CategoryResponse> getCategoryTree() {
        // 获取所有分类
        List<Category> allCategories = categoryMapper.selectAllCategories();
        
        // 转换为响应DTO
        List<CategoryResponse> allCategoryResponses = allCategories.stream()
                .map(this::convertToCategoryResponse)
                .collect(Collectors.toList());
        
        // 构建树形结构
        List<CategoryResponse> rootCategories = new ArrayList<>();
        
        // 首先找到所有一级分类
        for (CategoryResponse category : allCategoryResponses) {
            if (category.getLevel() == 1) {
                category.setChildren(new ArrayList<>());
                rootCategories.add(category);
            }
        }
        
        // 然后为每个一级分类添加子分类
        for (CategoryResponse rootCategory : rootCategories) {
            for (CategoryResponse category : allCategoryResponses) {
                if (category.getLevel() == 2 && rootCategory.getId().equals(category.getParentId())) {
                    rootCategory.getChildren().add(category);
                }
            }
        }
        
        return rootCategories;
    }

    /**
     * 转换为分类响应DTO
     */
    private CategoryResponse convertToCategoryResponse(Category category) {
        CategoryResponse response = new CategoryResponse();
        BeanUtils.copyProperties(category, response);
        
        // 如果没有工具数量字段，设置为0
        if (response.getToolCount() == null) {
            response.setToolCount(0);
        }
        
        return response;
    }
} 
package com.rpal.rpal_portal_backend.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户信息响应DTO
 */
@Data
@Schema(description = "用户信息响应")
public class UserInfoResponse {

    @Schema(description = "用户ID", example = "RPAL-A1B2C3D4E501")
    private String id;

    @Schema(description = "用户名", example = "john_doe")
    private String username;

    @Schema(description = "邮箱地址", example = "<EMAIL>")
    private String email;

    @Schema(description = "ORCID标识", example = "0000-0000-0000-0000")
    private String orcid;

    @Schema(description = "研究领域", example = "Computer Science")
    private String researchField;

    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatarUrl;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
}

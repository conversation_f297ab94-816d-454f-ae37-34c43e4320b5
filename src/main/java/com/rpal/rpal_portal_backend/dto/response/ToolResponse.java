package com.rpal.rpal_portal_backend.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 工具响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "工具信息响应")
public class ToolResponse {

    @Schema(description = "工具ID", example = "1")
    private Long id;

    @Schema(description = "工具名称", example = "R Studio")
    private String toolName;

    @Schema(description = "工具URL", example = "https://rstudio.com/")
    private String url;

    @Schema(description = "工具描述", example = "R语言集成开发环境")
    private String description;

    @Schema(description = "图标URL", example = "https://example.com/icon.png")
    private String iconUrl;

    @Schema(description = "状态", example = "ACTIVE")
    private String status;

    @Schema(description = "分类ID", example = "2")
    private Long categoryId;

    @Schema(description = "分类名称", example = "开发工具")
    private String categoryName;

    @Schema(description = "点击次数", example = "1250")
    private Integer clickCount;

    @Schema(description = "是否已收藏", example = "true")
    private Boolean isFavorited;

    @Schema(description = "收藏次数", example = "89")
    private Integer favoriteCount;

    @Schema(description = "最后检查时间", example = "2024-01-01T12:00:00")
    private LocalDateTime lastCheckTime;

    @Schema(description = "创建时间", example = "2024-01-01T12:00:00")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间", example = "2024-01-01T12:00:00")
    private LocalDateTime updatedAt;
}

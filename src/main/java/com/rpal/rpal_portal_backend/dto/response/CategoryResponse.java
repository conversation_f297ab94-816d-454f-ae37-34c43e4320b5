package com.rpal.rpal_portal_backend.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 分类响应DTO
 */
@Data
public class CategoryResponse {

    /**
     * 分类ID
     */
    private Long id;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 父级分类ID
     */
    private Long parentId;

    /**
     * 分类级别
     */
    private Integer level;

    /**
     * 描述
     */
    private String description;

    /**
     * 工具数量
     */
    private Integer toolCount;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
} 
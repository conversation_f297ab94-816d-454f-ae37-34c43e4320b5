package com.rpal.rpal_portal_backend.dto.request;

import com.rpal.rpal_portal_backend.common.validation.PasswordMatches;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

/**
 * 用户注册请求DTO
 */
@Data
@Schema(description = "用户注册请求")
@PasswordMatches
public class UserRegisterRequest {

    @Schema(description = "用户名", example = "john_doe", required = true)
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    private String username;

    @Schema(description = "邮箱地址", example = "<EMAIL>", required = true)
    @NotBlank(message = "邮箱地址不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;

    @Schema(description = "密码", example = "password123", required = true)
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
    private String password;

    @Schema(description = "确认密码", example = "password123", required = true)
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;

    @Schema(description = "验证码", example = "123456", required = true)
    @NotBlank(message = "验证码不能为空")
    @Pattern(regexp = "^\\d{6}$", message = "验证码必须是6位数字")
    private String verificationCode;

    @Schema(description = "ORCID标识", example = "0000-0000-0000-0000")
    @Pattern(regexp = "^\\d{4}-\\d{4}-\\d{4}-\\d{3}[0-9X]$", message = "ORCID格式不正确")
    private String orcid;

    @Schema(description = "研究领域", example = "Computer Science")
    @Size(max = 200, message = "研究领域长度不能超过200个字符")
    private String researchField;
}

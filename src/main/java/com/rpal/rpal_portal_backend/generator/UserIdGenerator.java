package com.rpal.rpal_portal_backend.generator;

import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;

/**
 * 用户ID生成器
 * 生成格式：RPAL-{10位随机字符}{2位计数器}
 * 示例：RPAL-A1B2C3D4E501
 */
@Slf4j
@Component("用户ID生成器")
public class UserIdGenerator implements IdentifierGenerator {

    // 静态前缀"RPAL-"
    private static final String PREFIX = "RPAL-";

    // 字符池（排除容易混淆的字符：0,1,I,O）
    private static final String CHAR_POOL = "23456789ABCDEFGHJKLMNPQRSTUVWXYZ";

    // 随机数生成器
    private static final SecureRandom RANDOM = new SecureRandom();

    // 计数器用于提高并发下的唯一性
    private static final AtomicInteger COUNTER = new AtomicInteger(0);

    // ID长度配置
    private static final int RANDOM_PART_LENGTH = 10;
    private static final int COUNTER_PART_LENGTH = 2;

    // ID验证正则表达式
    private static final Pattern USER_ID_PATTERN = Pattern.compile("^RPAL-[23456789ABCDEFGHJKLMNPQRSTUVWXYZ]{10}\\d{2}$");

    @Override
    public String nextUUID(Object entity) {
        return generateUserId();
    }

    /**
     * 生成用户ID
     * @return 用户ID
     */
    public static String generateUserId() {
        StringBuilder sb = new StringBuilder(PREFIX);

        // 生成10位随机字符
        for (int i = 0; i < RANDOM_PART_LENGTH; i++) {
            int index = RANDOM.nextInt(CHAR_POOL.length());
            sb.append(CHAR_POOL.charAt(index));
        }

        // 添加2位计数器，确保唯一性
        sb.append(String.format("%02d", COUNTER.getAndIncrement() % 100));

        String userId = sb.toString();
        log.debug("生成用户ID: {}", userId);
        return userId;
    }

    /**
     * 生成带时间戳的用户ID（用于特殊场景）
     * @return 带时间戳的用户ID
     */
    public static String generateUserIdWithTimestamp() {
        StringBuilder sb = new StringBuilder(PREFIX);

        // 添加时间戳（年月日时分）
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmm"));
        sb.append(timestamp);

        // 添加4位随机字符
        for (int i = 0; i < 4; i++) {
            int index = RANDOM.nextInt(CHAR_POOL.length());
            sb.append(CHAR_POOL.charAt(index));
        }

        String userId = sb.toString();
        log.debug("生成带时间戳的用户ID: {}", userId);
        return userId;
    }

    /**
     * 验证用户ID格式是否正确
     * @param userId 用户ID
     * @return 是否有效
     */
    public static boolean isValidUserId(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            return false;
        }
        return USER_ID_PATTERN.matcher(userId).matches();
    }

    /**
     * 提取用户ID中的随机部分
     * @param userId 用户ID
     * @return 随机部分
     */
    public static String extractRandomPart(String userId) {
        if (!isValidUserId(userId)) {
            return null;
        }
        return userId.substring(PREFIX.length(), PREFIX.length() + RANDOM_PART_LENGTH);
    }

    /**
     * 提取用户ID中的计数器部分
     * @param userId 用户ID
     * @return 计数器部分
     */
    public static String extractCounterPart(String userId) {
        if (!isValidUserId(userId)) {
            return null;
        }
        return userId.substring(PREFIX.length() + RANDOM_PART_LENGTH);
    }

    /**
     * 获取当前计数器值
     * @return 当前计数器值
     */
    public static int getCurrentCounter() {
        return COUNTER.get();
    }

    /**
     * 重置计数器（谨慎使用）
     */
    public static void resetCounter() {
        COUNTER.set(0);
        log.warn("用户ID生成器计数器已重置");
    }

    @Override
    public Number nextId(Object entity) {
        return null;
    }

    /**
     * 获取ID生成统计信息
     * @return 统计信息
     */
    public static String getGeneratorStats() {
        return String.format("UserIdGenerator Stats - Counter: %d, CharPool Size: %d, Pattern: %s",
                COUNTER.get(), CHAR_POOL.length(), USER_ID_PATTERN.pattern());
    }
}

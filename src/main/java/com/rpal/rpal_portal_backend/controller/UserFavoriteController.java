package com.rpal.rpal_portal_backend.controller;

import com.rpal.rpal_portal_backend.common.result.Result;
import com.rpal.rpal_portal_backend.common.utils.JwtUtils;
import com.rpal.rpal_portal_backend.dto.request.BatchFavoriteRequest;
import com.rpal.rpal_portal_backend.dto.request.FavoriteToolRequest;
import com.rpal.rpal_portal_backend.dto.response.ToolResponse;
import com.rpal.rpal_portal_backend.service.UserToolService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户收藏管理控制器
 */
@Tag(name = "用户收藏管理", description = "用户工具收藏相关的API接口")
@RestController
@RequestMapping("/users/favorites")
@RequiredArgsConstructor
@SecurityRequirement(name = "Bearer Authentication")
public class UserFavoriteController {

    private final UserToolService userToolService;
    private final JwtUtils jwtUtils;

    @Operation(summary = "收藏工具", description = "用户收藏指定的工具")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "收藏成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误或工具已收藏"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "404", description = "工具不存在")
    })
    @PostMapping
    public Result<Void> favoriteTools(@Valid @RequestBody FavoriteToolRequest request,
                                     HttpServletRequest httpRequest) {
        String userId = extractUserIdFromRequest(httpRequest);
        boolean success = userToolService.favoriteTools(userId, request.getToolId());
        
        if (success) {
            return Result.success("收藏成功");
        } else {
            return Result.error("收藏失败，可能已经收藏过该工具");
        }
    }

    @Operation(summary = "取消收藏工具", description = "用户取消收藏指定的工具")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "取消收藏成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误或工具未收藏"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "404", description = "工具不存在")
    })
    @DeleteMapping("/{toolId}")
    public Result<Void> unfavoriteTools(
            @Parameter(description = "工具ID", required = true) @PathVariable Long toolId,
            HttpServletRequest httpRequest) {
        String userId = extractUserIdFromRequest(httpRequest);
        boolean success = userToolService.unfavoriteTools(userId, toolId);
        
        if (success) {
            return Result.success("取消收藏成功");
        } else {
            return Result.error("取消收藏失败，可能未收藏该工具");
        }
    }

    @Operation(summary = "切换收藏状态", description = "切换工具的收藏状态（已收藏则取消，未收藏则收藏）")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "操作成功"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "404", description = "工具不存在")
    })
    @PostMapping("/{toolId}/toggle")
    public Result<Boolean> toggleFavorite(
            @Parameter(description = "工具ID", required = true) @PathVariable Long toolId,
            HttpServletRequest httpRequest) {
        String userId = extractUserIdFromRequest(httpRequest);
        boolean isFavorited = userToolService.toggleFavorite(userId, toolId);
        
        String message = isFavorited ? "收藏成功" : "取消收藏成功";
        return Result.success(message, isFavorited);
    }

    @Operation(summary = "检查收藏状态", description = "检查用户是否已收藏指定工具")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "检查成功"),
            @ApiResponse(responseCode = "401", description = "未授权")
    })
    @GetMapping("/{toolId}/status")
    public Result<Boolean> checkFavoriteStatus(
            @Parameter(description = "工具ID", required = true) @PathVariable Long toolId,
            HttpServletRequest httpRequest) {
        String userId = extractUserIdFromRequest(httpRequest);
        boolean isFavorited = userToolService.isFavorited(userId, toolId);
        return Result.success("检查收藏状态成功", isFavorited);
    }

    @Operation(summary = "获取收藏的工具列表", description = "获取当前用户收藏的所有工具")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(schema = @Schema(implementation = ToolResponse.class))),
            @ApiResponse(responseCode = "401", description = "未授权")
    })
    @GetMapping
    public Result<List<ToolResponse>> getUserFavoriteTools(HttpServletRequest httpRequest) {
        String userId = extractUserIdFromRequest(httpRequest);
        List<ToolResponse> favoriteTools = userToolService.getUserFavoriteTools(userId);
        return Result.success("获取收藏列表成功", favoriteTools);
    }

    @Operation(summary = "获取收藏统计", description = "获取用户收藏的工具数量统计")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "401", description = "未授权")
    })
    @GetMapping("/count")
    public Result<Integer> getUserFavoriteCount(HttpServletRequest httpRequest) {
        String userId = extractUserIdFromRequest(httpRequest);
        int count = userToolService.getUserFavoriteCount(userId);
        return Result.success("获取收藏统计成功", count);
    }

    @Operation(summary = "批量收藏工具", description = "批量收藏多个工具")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "批量收藏完成"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权")
    })
    @PostMapping("/batch")
    public Result<Integer> batchFavoriteTools(@Valid @RequestBody BatchFavoriteRequest request,
                                             HttpServletRequest httpRequest) {
        String userId = extractUserIdFromRequest(httpRequest);
        int successCount = userToolService.batchFavoriteTools(userId, request.getToolIds());
        return Result.success("批量收藏完成，成功收藏 " + successCount + " 个工具", successCount);
    }

    @Operation(summary = "批量取消收藏", description = "批量取消收藏多个工具")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "批量取消收藏完成"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权")
    })
    @DeleteMapping("/batch")
    public Result<Integer> batchUnfavoriteTools(@Valid @RequestBody BatchFavoriteRequest request,
                                               HttpServletRequest httpRequest) {
        String userId = extractUserIdFromRequest(httpRequest);
        int successCount = userToolService.batchUnfavoriteTools(userId, request.getToolIds());
        return Result.success("批量取消收藏完成，成功取消 " + successCount + " 个工具", successCount);
    }

    @Operation(summary = "清空所有收藏", description = "清空用户的所有收藏工具")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "清空成功"),
            @ApiResponse(responseCode = "401", description = "未授权")
    })
    @DeleteMapping("/clear")
    public Result<Integer> clearUserFavorites(HttpServletRequest httpRequest) {
        String userId = extractUserIdFromRequest(httpRequest);
        int clearedCount = userToolService.clearUserFavorites(userId);
        return Result.success("清空收藏成功，共清空 " + clearedCount + " 个工具", clearedCount);
    }

    /**
     * 从请求中提取用户ID
     */
    private String extractUserIdFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader(JwtUtils.TOKEN_HEADER);
        String token = jwtUtils.extractTokenFromHeader(authHeader);
        
        if (token != null && jwtUtils.validateToken(token) && jwtUtils.isAccessToken(token)) {
            return jwtUtils.getUserIdFromToken(token);
        }
        
        throw new RuntimeException("无效的认证信息");
    }
}

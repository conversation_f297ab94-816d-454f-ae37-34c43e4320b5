package com.rpal.rpal_portal_backend.controller;

import com.rpal.rpal_portal_backend.common.result.Result;
import com.rpal.rpal_portal_backend.dto.request.*;
import com.rpal.rpal_portal_backend.dto.response.AuthResponse;
import com.rpal.rpal_portal_backend.dto.response.UserInfoResponse;
import com.rpal.rpal_portal_backend.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@Tag(name = "用户管理", description = "用户注册、登录等相关的API接口")
@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    @Operation(summary = "发送邮箱验证码", description = "向指定邮箱发送6位数字验证码，用于用户注册")
    @PostMapping("/send-verification-code")
    public Result<Void> sendVerificationCode(@Valid @RequestBody SendVerificationCodeRequest request) {
        userService.sendVerificationCode(request.getEmail());
        return Result.success("验证码发送成功，请查收邮件");
    }

    @Operation(summary = "验证邮箱验证码", description = "验证用户输入的邮箱验证码是否正确")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "验证成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "422", description = "验证码错误或已过期")
    })
    @PostMapping("/verify-code")
    public Result<Void> verifyCode(@Valid @RequestBody VerifyCodeRequest request) {
        boolean isValid = userService.verifyCode(request.getEmail(), request.getCode());
        if (isValid) {
            return Result.success("验证码验证成功");
        } else {
            return Result.error("验证码错误或已过期");
        }
    }

    @Operation(summary = "用户注册", description = "用户注册接口，需要先验证邮箱验证码")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "注册成功",
                    content = @Content(schema = @Schema(implementation = UserInfoResponse.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "409", description = "用户已存在"),
            @ApiResponse(responseCode = "422", description = "验证码错误或已过期")
    })
    @PostMapping("/register")
    public Result<UserInfoResponse> register(@Valid @RequestBody UserRegisterRequest request) {
        UserInfoResponse userInfo = userService.register(request);
        return Result.success("注册成功", userInfo);
    }

    @Operation(summary = "用户登录", description = "用户登录接口，支持邮箱或用户名登录，返回JWT Token")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "登录成功",
                    content = @Content(schema = @Schema(implementation = AuthResponse.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "401", description = "用户名或密码错误"),
            @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @PostMapping("/login")
    public Result<AuthResponse> login(@Valid @RequestBody UserLoginRequest request) {
        AuthResponse authResponse = userService.login(request);
        return Result.success("登录成功", authResponse);
    }

    @Operation(summary = "获取用户信息", description = "根据用户ID获取用户详细信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(schema = @Schema(implementation = UserInfoResponse.class))),
            @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @GetMapping("/{userId}")
    public Result<UserInfoResponse> getUserInfo(@PathVariable String userId) {
        UserInfoResponse userInfo = userService.getUserInfo(userId);
        return Result.success(userInfo);
    }

    @Operation(summary = "检查邮箱是否可用", description = "检查邮箱是否已被注册")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "检查完成")
    })
    @GetMapping("/check-email")
    public Result<Boolean> checkEmailAvailable(@RequestParam String email) {
        boolean exists = userService.isEmailExists(email);
        return Result.success("邮箱检查完成", !exists);
    }

    @Operation(summary = "检查用户名是否可用", description = "检查用户名是否已被使用")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "检查完成")
    })
    @GetMapping("/check-username")
    public Result<Boolean> checkUsernameAvailable(@RequestParam String username) {
        boolean exists = userService.isUsernameExists(username);
        return Result.success("用户名检查完成", !exists);
    }

    @Operation(summary = "刷新Token", description = "使用刷新Token获取新的访问Token")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "刷新成功",
                    content = @Content(schema = @Schema(implementation = AuthResponse.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "401", description = "刷新Token无效或已过期"),
            @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @PostMapping("/refresh-token")
    public Result<AuthResponse> refreshToken(@Valid @RequestBody RefreshTokenRequest request) {
        AuthResponse authResponse = userService.refreshToken(request);
        return Result.success("Token刷新成功", authResponse);
    }
}

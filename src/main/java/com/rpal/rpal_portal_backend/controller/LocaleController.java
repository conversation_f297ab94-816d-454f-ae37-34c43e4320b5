package com.rpal.rpal_portal_backend.controller;

import com.rpal.rpal_portal_backend.common.result.Result;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.LocaleResolver;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

@RestController
@RequestMapping("/api/locale")
@CrossOrigin(origins = "*")
public class LocaleController {

    private final LocaleResolver localeResolver;

    public LocaleController(LocaleResolver localeResolver) {
        this.localeResolver = localeResolver;
    }

    /**
     * 切换语言
     * @param lang 语言代码 (en, zh)
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 响应结果
     */
    @PostMapping("/change")
    public Result<Map<String, Object>> changeLocale(
            @RequestParam String lang,
            HttpServletRequest request,
            HttpServletResponse response) {
        
        Locale locale;
        switch (lang.toLowerCase()) {
            case "zh":
                locale = Locale.SIMPLIFIED_CHINESE;
                break;
            case "en":
            default:
                locale = Locale.ENGLISH;
                break;
        }
        
        localeResolver.setLocale(request, response, locale);
        
        Map<String, Object> data = new HashMap<>();
        data.put("locale", lang);
        data.put("message", "Language changed successfully");
        
        return Result.success(data);
    }

    /**
     * 获取当前语言
     * @param request HTTP请求
     * @return 当前语言信息
     */
    @GetMapping("/current")
    public Result<Map<String, Object>> getCurrentLocale(HttpServletRequest request) {
        Locale currentLocale = localeResolver.resolveLocale(request);
        
        Map<String, Object> data = new HashMap<>();
        data.put("locale", currentLocale.getLanguage());
        data.put("displayName", currentLocale.getDisplayName(currentLocale));
        
        return Result.success(data);
    }

    /**
     * 获取支持的语言列表
     * @return 支持的语言列表
     */
    @GetMapping("/supported")
    public Result<Map<String, Object>> getSupportedLocales() {
        Map<String, Object> data = new HashMap<>();
        
        Map<String, String> locales = new HashMap<>();
        locales.put("en", "English");
        locales.put("zh", "中文");
        
        data.put("locales", locales);
        
        return Result.success(data);
    }
} 
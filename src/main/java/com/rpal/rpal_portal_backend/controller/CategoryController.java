package com.rpal.rpal_portal_backend.controller;

import com.rpal.rpal_portal_backend.common.result.Result;
import com.rpal.rpal_portal_backend.dto.response.CategoryResponse;
import com.rpal.rpal_portal_backend.service.CategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分类管理控制器
 */
@Tag(name = "分类管理", description = "分类查询等相关的API接口")
@RestController
@RequestMapping("/categories")
@RequiredArgsConstructor
public class CategoryController {

    private final CategoryService categoryService;

    @Operation(summary = "获取所有二级分类", description = "获取所有二级分类及其工具数量")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(schema = @Schema(implementation = CategoryResponse.class)))
    })
    @GetMapping("/sub")
    public Result<List<CategoryResponse>> getAllSubCategories() {
        List<CategoryResponse> categories = categoryService.getAllSubCategories();
        return Result.success("获取分类列表成功", categories);
    }

    @Operation(summary = "根据父级ID获取子分类", description = "根据父级分类ID获取子分类列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(schema = @Schema(implementation = CategoryResponse.class)))
    })
    @GetMapping("/parent/{parentId}")
    public Result<List<CategoryResponse>> getCategoriesByParentId(
            @Parameter(description = "父级分类ID", required = true) @PathVariable Long parentId) {
        List<CategoryResponse> categories = categoryService.getCategoriesByParentId(parentId);
        return Result.success("获取子分类列表成功", categories);
    }

    @Operation(summary = "根据ID获取分类详情", description = "根据分类ID获取分类详细信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(schema = @Schema(implementation = CategoryResponse.class))),
            @ApiResponse(responseCode = "404", description = "分类不存在")
    })
    @GetMapping("/{categoryId}")
    public Result<CategoryResponse> getCategoryById(
            @Parameter(description = "分类ID", required = true) @PathVariable Long categoryId) {
        CategoryResponse category = categoryService.getCategoryById(categoryId);
        return Result.success("获取分类详情成功", category);
    }
} 
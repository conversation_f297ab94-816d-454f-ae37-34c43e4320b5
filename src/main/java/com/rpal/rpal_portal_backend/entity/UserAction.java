package com.rpal.rpal_portal_backend.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户行为实体类 - 对应user_actions表
 */
@Data
@TableName("user_actions")
public class UserAction {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 工具ID
     */
    @TableField("tool_id")
    private Long toolId;

    /**
     * 行为类型（CLICK：点击，SEARCH：搜索，COLLECT：收藏）
     */
    @TableField("action_type")
    private String actionType;

    /**
     * 行为时间
     */
    @TableField("action_time")
    private LocalDateTime actionTime;

    /**
     * 元数据（JSON格式）
     */
    private String metadata;
}

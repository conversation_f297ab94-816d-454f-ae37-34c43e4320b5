package com.rpal.rpal_portal_backend.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 工具实体类 - 对应tools表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tools")
public class Tool extends BaseEntity {

    /**
     * 工具名称
     */
    @TableField("tool_name")
    private String toolName;

    /**
     * 工具URL
     */
    private String url;

    /**
     * 描述
     */
    private String description;

    /**
     * 图标URL
     */
    @TableField("icon_url")
    private String iconUrl;

    /**
     * 状态（ACTIVE：活跃，INACTIVE：非活跃）
     */
    private String status;

    /**
     * 分类ID
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 点击次数
     */
    @TableField("click_count")
    private Integer clickCount;

    /**
     * 最后检查时间
     */
    @TableField("last_check_time")
    private LocalDateTime lastCheckTime;
}

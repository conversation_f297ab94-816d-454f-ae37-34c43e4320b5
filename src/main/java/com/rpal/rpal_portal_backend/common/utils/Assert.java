package com.rpal.rpal_portal_backend.common.utils;

import com.rpal.rpal_portal_backend.common.enums.ResponseCode;
import com.rpal.rpal_portal_backend.common.exception.BusinessException;
import com.rpal.rpal_portal_backend.common.exception.ValidationException;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.Collection;

/**
 * 异常断言工具类
 * 提供便捷的断言方法，失败时抛出相应异常
 */
public class Assert {

    /**
     * 断言对象不为空
     */
    public static void notNull(Object object, String message) {
        if (object == null) {
            throw new ValidationException(message);
        }
    }

    public static void notNull(Object object, ResponseCode responseCode) {
        if (object == null) {
            throw new BusinessException(responseCode);
        }
    }

    /**
     * 断言字符串不为空
     */
    public static void hasText(String text, String message) {
        if (!StringUtils.hasText(text)) {
            throw new ValidationException(message);
        }
    }

    public static void hasText(String text, ResponseCode responseCode) {
        if (!StringUtils.hasText(text)) {
            throw new BusinessException(responseCode);
        }
    }

    /**
     * 断言集合不为空
     */
    public static void notEmpty(Collection<?> collection, String message) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new ValidationException(message);
        }
    }

    public static void notEmpty(Collection<?> collection, ResponseCode responseCode) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new BusinessException(responseCode);
        }
    }

    /**
     * 断言数组不为空
     */
    public static void notEmpty(Object[] array, String message) {
        if (ObjectUtils.isEmpty(array)) {
            throw new ValidationException(message);
        }
    }

    public static void notEmpty(Object[] array, ResponseCode responseCode) {
        if (ObjectUtils.isEmpty(array)) {
            throw new BusinessException(responseCode);
        }
    }

    /**
     * 断言表达式为真
     */
    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            throw new ValidationException(message);
        }
    }

    public static void isTrue(boolean expression, ResponseCode responseCode) {
        if (!expression) {
            throw new BusinessException(responseCode);
        }
    }

    /**
     * 断言表达式为假
     */
    public static void isFalse(boolean expression, String message) {
        if (expression) {
            throw new ValidationException(message);
        }
    }

    public static void isFalse(boolean expression, ResponseCode responseCode) {
        if (expression) {
            throw new BusinessException(responseCode);
        }
    }

    /**
     * 断言两个对象相等
     */
    public static void equals(Object obj1, Object obj2, String message) {
        if (!ObjectUtils.nullSafeEquals(obj1, obj2)) {
            throw new ValidationException(message);
        }
    }

    public static void equals(Object obj1, Object obj2, ResponseCode responseCode) {
        if (!ObjectUtils.nullSafeEquals(obj1, obj2)) {
            throw new BusinessException(responseCode);
        }
    }

    /**
     * 断言两个对象不相等
     */
    public static void notEquals(Object obj1, Object obj2, String message) {
        if (ObjectUtils.nullSafeEquals(obj1, obj2)) {
            throw new ValidationException(message);
        }
    }

    public static void notEquals(Object obj1, Object obj2, ResponseCode responseCode) {
        if (ObjectUtils.nullSafeEquals(obj1, obj2)) {
            throw new BusinessException(responseCode);
        }
    }

    /**
     * 断言数字为正数
     */
    public static void isPositive(Number number, String message) {
        if (number == null || number.doubleValue() <= 0) {
            throw new ValidationException(message);
        }
    }

    /**
     * 断言数字不为负数
     */
    public static void isNotNegative(Number number, String message) {
        if (number == null || number.doubleValue() < 0) {
            throw new ValidationException(message);
        }
    }

    /**
     * 断言字符串长度在指定范围内
     */
    public static void lengthBetween(String text, int min, int max, String message) {
        if (text == null || text.length() < min || text.length() > max) {
            throw new ValidationException(message);
        }
    }

    /**
     * 断言数字在指定范围内
     */
    public static void between(Number number, Number min, Number max, String message) {
        if (number == null || 
            number.doubleValue() < min.doubleValue() || 
            number.doubleValue() > max.doubleValue()) {
            throw new ValidationException(message);
        }
    }

    // ========== 业务断言方法 ==========

    /**
     * 断言用户存在
     */
    public static void userExists(Object user) {
        notNull(user, ResponseCode.USER_NOT_FOUND);
    }

    /**
     * 断言工具存在
     */
    public static void toolExists(Object tool) {
        notNull(tool, ResponseCode.TOOL_NOT_FOUND);
    }

    /**
     * 断言分类存在
     */
    public static void categoryExists(Object category) {
        notNull(category, ResponseCode.CATEGORY_NOT_FOUND);
    }

    /**
     * 断言新闻存在
     */
    public static void newsExists(Object news) {
        notNull(news, ResponseCode.NEWS_NOT_FOUND);
    }

    /**
     * 断言员工存在
     */
    public static void staffExists(Object staff) {
        notNull(staff, ResponseCode.STAFF_NOT_FOUND);
    }

    /**
     * 断言配置存在
     */
    public static void configExists(Object config) {
        notNull(config, ResponseCode.CONFIG_NOT_FOUND);
    }
}

package com.rpal.rpal_portal_backend.common.exception;

import com.rpal.rpal_portal_backend.common.enums.ResponseCode;

/**
 * 业务异常类
 * 用于处理业务逻辑中的异常情况
 */
public class BusinessException extends BaseException {

    private static final long serialVersionUID = 1L;

    public BusinessException() {
        super();
    }

    public BusinessException(String message) {
        super(message);
    }

    public BusinessException(Integer code, String message) {
        super(code, message);
    }

    public BusinessException(ResponseCode responseCode) {
        super(responseCode);
    }

    public BusinessException(String message, Throwable cause) {
        super(message, cause);
    }

    public BusinessException(Integer code, String message, Throwable cause) {
        super(code, message, cause);
    }

    public BusinessException(ResponseCode responseCode, Throwable cause) {
        super(responseCode, cause);
    }

    // ========== 静态工厂方法 ==========

    /**
     * 用户相关异常
     */
    public static BusinessException userNotFound() {
        return new BusinessException(ResponseCode.USER_NOT_FOUND);
    }

    public static BusinessException userAlreadyExists() {
        return new BusinessException(ResponseCode.USER_ALREADY_EXISTS);
    }

    public static BusinessException passwordError() {
        return new BusinessException(ResponseCode.PASSWORD_ERROR);
    }

    public static BusinessException userDisabled() {
        return new BusinessException(ResponseCode.USER_DISABLED);
    }

    /**
     * 工具相关异常
     */
    public static BusinessException toolNotFound() {
        return new BusinessException(ResponseCode.TOOL_NOT_FOUND);
    }

    public static BusinessException toolUnavailable() {
        return new BusinessException(ResponseCode.TOOL_UNAVAILABLE);
    }

    /**
     * 分类相关异常
     */
    public static BusinessException categoryNotFound() {
        return new BusinessException(ResponseCode.CATEGORY_NOT_FOUND);
    }

    public static BusinessException categoryHasChildren() {
        return new BusinessException(ResponseCode.CATEGORY_HAS_CHILDREN);
    }

    /**
     * 新闻相关异常
     */
    public static BusinessException newsNotFound() {
        return new BusinessException(ResponseCode.NEWS_NOT_FOUND);
    }

    /**
     * 员工相关异常
     */
    public static BusinessException staffNotFound() {
        return new BusinessException(ResponseCode.STAFF_NOT_FOUND);
    }

    public static BusinessException staffAlreadyExists() {
        return new BusinessException(ResponseCode.STAFF_ALREADY_EXISTS);
    }

    /**
     * 配置相关异常
     */
    public static BusinessException configNotFound() {
        return new BusinessException(ResponseCode.CONFIG_NOT_FOUND);
    }
}

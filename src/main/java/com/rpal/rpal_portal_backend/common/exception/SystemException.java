package com.rpal.rpal_portal_backend.common.exception;

import com.rpal.rpal_portal_backend.common.enums.ResponseCode;

/**
 * 系统异常类
 * 用于处理系统级别的异常情况
 */
public class SystemException extends BaseException {

    private static final long serialVersionUID = 1L;

    public SystemException() {
        super();
    }

    public SystemException(String message) {
        super(message);
    }

    public SystemException(Integer code, String message) {
        super(code, message);
    }

    public SystemException(ResponseCode responseCode) {
        super(responseCode);
    }

    public SystemException(String message, Throwable cause) {
        super(message, cause);
    }

    public SystemException(Integer code, String message, Throwable cause) {
        super(code, message, cause);
    }

    public SystemException(ResponseCode responseCode, Throwable cause) {
        super(responseCode, cause);
    }

    // ========== 静态工厂方法 ==========

    /**
     * 数据库异常
     */
    public static SystemException databaseError() {
        return new SystemException(ResponseCode.DATABASE_ERROR);
    }

    public static SystemException databaseError(Throwable cause) {
        return new SystemException(ResponseCode.DATABASE_ERROR, cause);
    }

    /**
     * 缓存异常
     */
    public static SystemException cacheError() {
        return new SystemException(ResponseCode.CACHE_ERROR);
    }

    public static SystemException cacheError(Throwable cause) {
        return new SystemException(ResponseCode.CACHE_ERROR, cause);
    }

    /**
     * 文件上传异常
     */
    public static SystemException fileUploadError() {
        return new SystemException(ResponseCode.FILE_UPLOAD_ERROR);
    }

    public static SystemException fileUploadError(Throwable cause) {
        return new SystemException(ResponseCode.FILE_UPLOAD_ERROR, cause);
    }

    /**
     * 文件删除异常
     */
    public static SystemException fileDeleteError() {
        return new SystemException(ResponseCode.FILE_DELETE_ERROR);
    }

    public static SystemException fileDeleteError(Throwable cause) {
        return new SystemException(ResponseCode.FILE_DELETE_ERROR, cause);
    }

    /**
     * 服务不可用异常
     */
    public static SystemException serviceUnavailable() {
        return new SystemException(ResponseCode.SERVICE_UNAVAILABLE);
    }

    public static SystemException serviceUnavailable(Throwable cause) {
        return new SystemException(ResponseCode.SERVICE_UNAVAILABLE, cause);
    }
}

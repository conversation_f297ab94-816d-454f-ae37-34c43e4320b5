package com.rpal.rpal_portal_backend.common.exception;

import com.rpal.rpal_portal_backend.common.enums.ResponseCode;

/**
 * 参数校验异常类
 * 用于处理参数校验失败的异常情况
 */
public class ValidationException extends BaseException {

    private static final long serialVersionUID = 1L;

    public ValidationException() {
        super(ResponseCode.VALIDATION_ERROR);
    }

    public ValidationException(String message) {
        super(ResponseCode.VALIDATION_ERROR.getCode(), message);
    }

    public ValidationException(String message, Throwable cause) {
        super(ResponseCode.VALIDATION_ERROR.getCode(), message, cause);
    }

    // ========== 静态工厂方法 ==========

    /**
     * 参数为空异常
     */
    public static ValidationException parameterNull(String parameterName) {
        return new ValidationException(String.format("参数 %s 不能为空", parameterName));
    }

    /**
     * 参数格式错误异常
     */
    public static ValidationException parameterFormat(String parameterName) {
        return new ValidationException(String.format("参数 %s 格式错误", parameterName));
    }

    /**
     * 参数值无效异常
     */
    public static ValidationException parameterInvalid(String parameterName, String reason) {
        return new ValidationException(String.format("参数 %s 无效: %s", parameterName, reason));
    }

    /**
     * 参数长度超限异常
     */
    public static ValidationException parameterTooLong(String parameterName, int maxLength) {
        return new ValidationException(String.format("参数 %s 长度不能超过 %d 个字符", parameterName, maxLength));
    }

    /**
     * 参数长度不足异常
     */
    public static ValidationException parameterTooShort(String parameterName, int minLength) {
        return new ValidationException(String.format("参数 %s 长度不能少于 %d 个字符", parameterName, minLength));
    }

    /**
     * 参数值超出范围异常
     */
    public static ValidationException parameterOutOfRange(String parameterName, String range) {
        return new ValidationException(String.format("参数 %s 值超出范围: %s", parameterName, range));
    }

    /**
     * 邮箱格式错误异常
     */
    public static ValidationException emailFormat() {
        return new ValidationException("邮箱格式错误");
    }

    /**
     * 手机号格式错误异常
     */
    public static ValidationException phoneFormat() {
        return new ValidationException("手机号格式错误");
    }

    /**
     * URL格式错误异常
     */
    public static ValidationException urlFormat() {
        return new ValidationException("URL格式错误");
    }
}

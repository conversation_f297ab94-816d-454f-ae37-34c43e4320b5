package com.rpal.rpal_portal_backend.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 */
@Getter
@AllArgsConstructor
public enum ResponseCode {

    // 成功
    SUCCESS(200, "操作成功"),
    
    // 客户端错误 4xx
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    VALIDATION_ERROR(422, "参数校验失败"),
    
    // 服务器错误 5xx
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    
    // 业务错误 1xxx
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_ALREADY_EXISTS(1002, "用户已存在"),
    PASSWORD_ERROR(1003, "密码错误"),
    USER_DISABLED(1004, "用户已被禁用"),
    
    TOOL_NOT_FOUND(1101, "工具不存在"),
    TOOL_UNAVAILABLE(1102, "工具不可用"),
    
    CATEGORY_NOT_FOUND(1201, "分类不存在"),
    CATEGORY_HAS_CHILDREN(1202, "分类下存在子分类，无法删除"),
    
    NEWS_NOT_FOUND(1301, "新闻不存在"),
    
    STAFF_NOT_FOUND(1401, "员工不存在"),
    STAFF_ALREADY_EXISTS(1402, "员工已存在"),
    
    CONFIG_NOT_FOUND(1501, "配置项不存在"),
    
    // 系统错误 9xxx
    DATABASE_ERROR(9001, "数据库操作失败"),
    CACHE_ERROR(9002, "缓存操作失败"),
    FILE_UPLOAD_ERROR(9003, "文件上传失败"),
    FILE_DELETE_ERROR(9004, "文件删除失败");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String message;
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rpal.rpal_portal_backend.mapper.CategoryMapper">

    <!-- 获取所有二级分类 -->
    <select id="selectAllSubCategories" resultType="com.rpal.rpal_portal_backend.entity.Category">
        SELECT 
            id, name, parent_id, level, description, created_at
        FROM categories 
        WHERE level = 2 AND deleted = 0
        ORDER BY id ASC
    </select>

    <!-- 根据父级ID获取子分类 -->
    <select id="selectByParentId" resultType="com.rpal.rpal_portal_backend.entity.Category">
        SELECT 
            id, name, parent_id, level, description, created_at
        FROM categories 
        WHERE parent_id = #{parentId} AND deleted = 0
        ORDER BY id ASC
    </select>

    <!-- 获取分类及其工具数量 -->
    <select id="selectCategoriesWithToolCount" resultType="com.rpal.rpal_portal_backend.entity.Category">
        SELECT 
            c.id, c.name, c.parent_id, c.level, c.description, c.created_at, COUNT(t.id) as toolCount
        FROM categories c
        LEFT JOIN tools t ON c.id = t.category_id AND t.deleted = 0 AND t.status = 'ACTIVE'
        WHERE c.level = 2 AND c.deleted = 0
        GROUP BY c.id, c.name, c.parent_id, c.level, c.description, c.created_at
        ORDER BY c.id ASC
    </select>

    <!-- 根据分类ID获取分类信息 -->
    <select id="selectById" resultType="com.rpal.rpal_portal_backend.entity.Category">
        SELECT 
            id, name, parent_id, level, description, created_at
        FROM categories 
        WHERE id = #{id} AND deleted = 0
    </select>

    <!-- 根据分类ID获取工具数量 -->
    <select id="countToolsByCategoryId" resultType="java.lang.Integer">
        SELECT
            COUNT(id)
        FROM tools
        WHERE category_id = #{categoryId} AND deleted = 0 AND status = 'ACTIVE'
    </select>

    <!-- 获取所有分类 -->
    <select id="selectAllCategories" resultType="com.rpal.rpal_portal_backend.entity.Category">
        SELECT 
            c.id, c.name, c.parent_id, c.level, c.description, c.created_at, COUNT(t.id) as toolCount
        FROM categories c
        LEFT JOIN tools t ON c.id = t.category_id AND t.deleted = 0 AND t.status = 'ACTIVE'
        WHERE c.deleted = 0
        GROUP BY c.id, c.name, c.parent_id, c.level, c.description, c.created_at
        ORDER BY c.level ASC, c.id ASC
    </select>

</mapper> 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rpal.rpal_portal_backend.mapper.UserActionMapper">

    <!-- 检查用户在指定日期是否已经点击过某个工具 -->
    <select id="existsClickByUserAndToolAndDate" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM user_actions 
        WHERE user_id = #{userId} 
          AND tool_id = #{toolId}
          AND action_type = 'CLICK'
          AND DATE(action_time) = #{date}
          LIMIT 1
    </select>

    <!-- 插入用户行为记录 -->
    <insert id="insertUserAction">
        INSERT INTO user_actions (user_id, tool_id, action_type, action_time, metadata)
        VALUES (#{userId}, #{toolId}, #{actionType}, #{actionTime}, #{metadata})
    </insert>

</mapper>
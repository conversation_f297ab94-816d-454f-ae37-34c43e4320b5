<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rpal.rpal_portal_backend.mapper.UserMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.rpal.rpal_portal_backend.entity.User">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="orcid" property="orcid" jdbcType="VARCHAR"/>
        <result column="password_hash" property="passwordHash" jdbcType="VARCHAR"/>
        <result column="research_field" property="researchField" jdbcType="VARCHAR"/>
        <result column="avatar_url" property="avatarUrl" jdbcType="VARCHAR"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
        <result column="deleted" property="deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, username, email, orcid, password_hash, research_field, avatar_url, created_at, updated_at, deleted
    </sql>

    <!-- 根据邮箱查询用户 -->
    <select id="selectByEmail" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM users
        WHERE email = #{email}
        AND deleted = 0
    </select>

    <!-- 根据用户名查询用户 -->
    <select id="selectByUsername" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM users
        WHERE username = #{username}
        AND deleted = 0
    </select>

    <!-- 根据邮箱或用户名查询用户 -->
    <select id="selectByEmailOrUsername" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM users
        WHERE (email = #{emailOrUsername} OR username = #{emailOrUsername})
        AND deleted = 0
    </select>

    <!-- 检查邮箱是否已存在 -->
    <select id="existsByEmail" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM users
        WHERE email = #{email}
        AND deleted = 0
    </select>

    <!-- 检查用户名是否已存在 -->
    <select id="existsByUsername" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM users
        WHERE username = #{username}
        AND deleted = 0
    </select>

    <!-- 检查ORCID是否已存在 -->
    <select id="existsByOrcid" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM users
        WHERE orcid = #{orcid}
        AND deleted = 0
    </select>

</mapper>

-- RPAL门户网站数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS rpal DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE rpal;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(20) PRIMARY KEY COMMENT '用户ID，格式：RPAL-{10位随机字符}{2位计数器}',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱地址',
    orcid VARCHAR(19) UNIQUE COMMENT 'ORCID标识，格式：0000-0000-0000-0000',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    research_field VARCHAR(200) COMMENT '研究领域',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标志（0：未删除，1：已删除）',
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_orcid (orcid),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 分类表
CREATE TABLE IF NOT EXISTS categories (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    parent_id BIGINT COMMENT '父级分类ID',
    level INT DEFAULT 1 COMMENT '分类级别',
    description TEXT COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标志（0：未删除，1：已删除）',
    INDEX idx_parent_id (parent_id),
    INDEX idx_level (level),
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类表';

-- 工具表
CREATE TABLE IF NOT EXISTS tools (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    tool_name VARCHAR(200) NOT NULL COMMENT '工具名称',
    url VARCHAR(500) NOT NULL COMMENT '工具URL',
    description TEXT COMMENT '描述',
    icon_url VARCHAR(500) COMMENT '图标URL',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态（ACTIVE：活跃，INACTIVE：非活跃）',
    category_id BIGINT COMMENT '分类ID',
    click_count INT DEFAULT 0 COMMENT '点击次数',
    last_check_time TIMESTAMP COMMENT '最后检查时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标志（0：未删除，1：已删除）',
    INDEX idx_category_id (category_id),
    INDEX idx_status (status),
    INDEX idx_tool_name (tool_name),
    INDEX idx_click_count (click_count),
    FOREIGN KEY (category_id) REFERENCES categories(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工具表';

-- 新闻表
CREATE TABLE IF NOT EXISTS news (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    title VARCHAR(200) NOT NULL COMMENT '标题',
    content LONGTEXT COMMENT '内容',
    cover_image_url VARCHAR(500) COMMENT '封面图片URL',
    publish_time TIMESTAMP COMMENT '发布时间',
    is_top BOOLEAN DEFAULT FALSE COMMENT '是否置顶（0：否，1：是）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标志（0：未删除，1：已删除）',
    INDEX idx_publish_time (publish_time),
    INDEX idx_is_top (is_top),
    INDEX idx_title (title)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻表';

-- 员工表
CREATE TABLE IF NOT EXISTS staff (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    real_name VARCHAR(100) COMMENT '真实姓名',
    role VARCHAR(20) DEFAULT 'EDITOR' COMMENT '角色（ADMIN：管理员，EDITOR：编辑）',
    last_login_time TIMESTAMP COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标志（0：未删除，1：已删除）',
    INDEX idx_username (username),
    INDEX idx_role (role)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='员工表';

-- 用户行为表
CREATE TABLE IF NOT EXISTS user_actions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(20) NOT NULL COMMENT '用户ID',
    tool_id BIGINT COMMENT '工具ID',
    action_type VARCHAR(20) NOT NULL COMMENT '行为类型（CLICK：点击，SEARCH：搜索，COLLECT：收藏）',
    action_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '行为时间',
    metadata JSON COMMENT '元数据（JSON格式）',
    INDEX idx_user_id (user_id),
    INDEX idx_tool_id (tool_id),
    INDEX idx_action_type (action_type),
    INDEX idx_action_time (action_time),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (tool_id) REFERENCES tools(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户行为表';

-- 用户工具关联表
CREATE TABLE IF NOT EXISTS user_tools (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(20) NOT NULL COMMENT '用户ID',
    tool_id BIGINT NOT NULL COMMENT '工具ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_user_tool (user_id, tool_id),
    INDEX idx_user_id (user_id),
    INDEX idx_tool_id (tool_id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (tool_id) REFERENCES tools(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户工具关联表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(500) COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 插入初始数据

-- 插入示例工具
INSERT INTO tools (tool_name, url, description, category_id, status) VALUES
('R Studio', 'https://rstudio.com/', 'R语言集成开发环境，提供数据分析和可视化功能', 15, 'ACTIVE'),
('Python', 'https://www.python.org/', 'Python编程语言，广泛应用于数据科学和机器学习', 21, 'ACTIVE'),
('TensorFlow', 'https://tensorflow.org/', 'Google开发的深度学习框架', 7, 'ACTIVE'),
('PyTorch', 'https://pytorch.org/', 'Facebook开发的深度学习框架', 7, 'ACTIVE'),
('Zotero', 'https://www.zotero.org/', '免费的文献管理工具', 12, 'ACTIVE'),
('Google Docs', 'https://docs.google.com/', '在线文档编辑工具', 18, 'ACTIVE'),
('Trello', 'https://trello.com/', '看板式项目管理工具', 13, 'ACTIVE'),
('Jupyter Notebook', 'https://jupyter.org/', '交互式编程环境', 22, 'ACTIVE'),
('Tableau', 'https://www.tableau.com/', '数据可视化工具', 17, 'ACTIVE'),
('GitHub', 'https://github.com/', '代码托管和版本控制平台', 15, 'ACTIVE'),
('MATLAB', 'https://www.mathworks.com/', '数值计算和科学计算软件', 11, 'ACTIVE'),
('SPSS', 'https://www.ibm.com/spss', '统计分析软件', 8, 'ACTIVE'),
('EndNote', 'https://endnote.com/', '文献管理和引用工具', 12, 'ACTIVE'),
('Slack', 'https://slack.com/', '团队沟通协作平台', 14, 'ACTIVE'),
('Docker', 'https://www.docker.com/', '容器化部署工具', 24, 'ACTIVE'),
('Visual Studio Code', 'https://code.visualstudio.com/', '轻量级代码编辑器', 22, 'ACTIVE'),
('Pandas', 'https://pandas.pydata.org/', 'Python数据分析库', 7, 'ACTIVE'),
('NumPy', 'https://numpy.org/', 'Python数值计算库', 11, 'ACTIVE'),
('Scikit-learn', 'https://scikit-learn.org/', 'Python机器学习库', 7, 'ACTIVE'),
('Plotly', 'https://plotly.com/', '交互式数据可视化库', 18, 'ACTIVE');


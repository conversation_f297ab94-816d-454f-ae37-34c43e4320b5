# 用户工具收藏功能使用指南

## 🎯 功能概述

用户工具收藏功能允许注册用户收藏感兴趣的工具，方便后续快速访问。该功能包含完整的收藏管理体系，支持单个收藏、批量操作、收藏统计等。

## ✨ 主要功能特性

### 1. 工具浏览功能
- ✅ 查看所有活跃工具
- ✅ 按分类浏览工具
- ✅ 搜索工具（支持名称和描述搜索）
- ✅ 查看热门工具（按点击次数排序）
- ✅ 工具详情查看
- ✅ 点击统计功能

### 2. 收藏管理功能
- ✅ 收藏/取消收藏工具
- ✅ 切换收藏状态
- ✅ 查看收藏状态
- ✅ 获取收藏列表
- ✅ 收藏统计
- ✅ 批量收藏操作
- ✅ 批量取消收藏
- ✅ 清空所有收藏

### 3. 智能显示功能
- ✅ 登录用户显示收藏状态
- ✅ 未登录用户正常浏览工具
- ✅ 工具被收藏次数统计
- ✅ 个人收藏数量统计

## 📋 API接口清单

### 工具浏览接口（无需登录）

| 方法 | 路径 | 描述 | 认证要求 |
|------|------|------|----------|
| GET | `/tools` | 获取所有活跃工具 | 无 |
| GET | `/tools/{toolId}` | 获取工具详情 | 无 |
| GET | `/tools/category/{categoryId}` | 按分类获取工具 | 无 |
| GET | `/tools/search?keyword={keyword}` | 搜索工具 | 无 |
| GET | `/tools/popular?limit={limit}` | 获取热门工具 | 无 |
| POST | `/tools/{toolId}/click` | 记录工具点击 | 需要JWT |

### 收藏管理接口（需要登录）

| 方法 | 路径 | 描述 | 认证要求 |
|------|------|------|----------|
| POST | `/users/favorites` | 收藏工具 | 需要JWT |
| DELETE | `/users/favorites/{toolId}` | 取消收藏工具 | 需要JWT |
| POST | `/users/favorites/{toolId}/toggle` | 切换收藏状态 | 需要JWT |
| GET | `/users/favorites/{toolId}/status` | 检查收藏状态 | 需要JWT |
| GET | `/users/favorites` | 获取收藏列表 | 需要JWT |
| GET | `/users/favorites/count` | 获取收藏统计 | 需要JWT |
| POST | `/users/favorites/batch` | 批量收藏 | 需要JWT |
| DELETE | `/users/favorites/batch` | 批量取消收藏 | 需要JWT |
| DELETE | `/users/favorites/clear` | 清空所有收藏 | 需要JWT |

## 🚀 使用示例

### 1. 前端JavaScript示例

```javascript
// API基础配置
const API_BASE = 'http://localhost:8081/rpal_portal';
const getAuthHeaders = () => ({
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
});

// 获取所有工具（支持未登录用户）
const getAllTools = async () => {
  const headers = { 'Content-Type': 'application/json' };
  const token = localStorage.getItem('accessToken');
  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }
  
  const response = await fetch(`${API_BASE}/tools`, { headers });
  return response.json();
};

// 搜索工具
const searchTools = async (keyword) => {
  const headers = { 'Content-Type': 'application/json' };
  const token = localStorage.getItem('accessToken');
  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }
  
  const response = await fetch(`${API_BASE}/tools/search?keyword=${encodeURIComponent(keyword)}`, { headers });
  return response.json();
};

// 收藏工具
const favoriteTools = async (toolId) => {
  const response = await fetch(`${API_BASE}/users/favorites`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify({ toolId })
  });
  return response.json();
};

// 取消收藏
const unfavoriteTools = async (toolId) => {
  const response = await fetch(`${API_BASE}/users/favorites/${toolId}`, {
    method: 'DELETE',
    headers: getAuthHeaders()
  });
  return response.json();
};

// 切换收藏状态
const toggleFavorite = async (toolId) => {
  const response = await fetch(`${API_BASE}/users/favorites/${toolId}/toggle`, {
    method: 'POST',
    headers: getAuthHeaders()
  });
  return response.json();
};

// 获取用户收藏列表
const getUserFavorites = async () => {
  const response = await fetch(`${API_BASE}/users/favorites`, {
    headers: getAuthHeaders()
  });
  return response.json();
};

// 批量收藏
const batchFavorite = async (toolIds) => {
  const response = await fetch(`${API_BASE}/users/favorites/batch`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify({ toolIds })
  });
  return response.json();
};
```

### 2. React组件示例

```jsx
import React, { useState, useEffect } from 'react';

const ToolCard = ({ tool, onFavoriteChange }) => {
  const [isFavorited, setIsFavorited] = useState(tool.isFavorited);
  const [isLoading, setIsLoading] = useState(false);

  const handleFavoriteClick = async () => {
    setIsLoading(true);
    try {
      const result = await toggleFavorite(tool.id);
      if (result.code === 200) {
        setIsFavorited(result.data);
        onFavoriteChange && onFavoriteChange(tool.id, result.data);
      }
    } catch (error) {
      console.error('收藏操作失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToolClick = async () => {
    // 记录点击
    try {
      await fetch(`${API_BASE}/tools/${tool.id}/click`, {
        method: 'POST',
        headers: getAuthHeaders()
      });
    } catch (error) {
      console.error('记录点击失败:', error);
    }
    
    // 打开工具链接
    window.open(tool.url, '_blank');
  };

  return (
    <div className="tool-card">
      <div className="tool-header">
        <img src={tool.iconUrl} alt={tool.toolName} />
        <h3>{tool.toolName}</h3>
        <button 
          className={`favorite-btn ${isFavorited ? 'favorited' : ''}`}
          onClick={handleFavoriteClick}
          disabled={isLoading}
        >
          {isFavorited ? '❤️' : '🤍'}
        </button>
      </div>
      <p className="tool-description">{tool.description}</p>
      <div className="tool-stats">
        <span>点击: {tool.clickCount}</span>
        <span>收藏: {tool.favoriteCount}</span>
      </div>
      <button className="tool-link-btn" onClick={handleToolClick}>
        访问工具
      </button>
    </div>
  );
};

const ToolList = () => {
  const [tools, setTools] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchKeyword, setSearchKeyword] = useState('');

  useEffect(() => {
    loadTools();
  }, []);

  const loadTools = async () => {
    try {
      const result = await getAllTools();
      if (result.code === 200) {
        setTools(result.data);
      }
    } catch (error) {
      console.error('加载工具失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchKeyword.trim()) {
      loadTools();
      return;
    }
    
    try {
      const result = await searchTools(searchKeyword);
      if (result.code === 200) {
        setTools(result.data);
      }
    } catch (error) {
      console.error('搜索失败:', error);
    }
  };

  const handleFavoriteChange = (toolId, isFavorited) => {
    setTools(prevTools => 
      prevTools.map(tool => 
        tool.id === toolId 
          ? { ...tool, isFavorited, favoriteCount: tool.favoriteCount + (isFavorited ? 1 : -1) }
          : tool
      )
    );
  };

  if (loading) return <div>加载中...</div>;

  return (
    <div className="tool-list">
      <div className="search-bar">
        <input
          type="text"
          placeholder="搜索工具..."
          value={searchKeyword}
          onChange={(e) => setSearchKeyword(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
        />
        <button onClick={handleSearch}>搜索</button>
      </div>
      
      <div className="tools-grid">
        {tools.map(tool => (
          <ToolCard 
            key={tool.id} 
            tool={tool} 
            onFavoriteChange={handleFavoriteChange}
          />
        ))}
      </div>
    </div>
  );
};
```

### 3. Vue.js组件示例

```vue
<template>
  <div class="tool-management">
    <!-- 搜索栏 -->
    <div class="search-section">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索工具..."
        @keyup.enter="handleSearch"
      >
        <template #append>
          <el-button @click="handleSearch">搜索</el-button>
        </template>
      </el-input>
    </div>

    <!-- 工具列表 -->
    <div class="tools-grid">
      <el-card 
        v-for="tool in tools" 
        :key="tool.id" 
        class="tool-card"
        shadow="hover"
      >
        <template #header>
          <div class="tool-header">
            <span>{{ tool.toolName }}</span>
            <el-button
              :type="tool.isFavorited ? 'danger' : 'primary'"
              :icon="tool.isFavorited ? 'Heart' : 'HeartEmpty'"
              circle
              @click="toggleFavorite(tool)"
            />
          </div>
        </template>
        
        <p>{{ tool.description }}</p>
        
        <div class="tool-stats">
          <el-tag>点击: {{ tool.clickCount }}</el-tag>
          <el-tag type="success">收藏: {{ tool.favoriteCount }}</el-tag>
        </div>
        
        <el-button 
          type="primary" 
          @click="visitTool(tool)"
          style="width: 100%; margin-top: 10px;"
        >
          访问工具
        </el-button>
      </el-card>
    </div>

    <!-- 我的收藏 -->
    <div class="favorites-section" v-if="isLoggedIn">
      <h3>我的收藏 ({{ favoriteCount }})</h3>
      <el-button @click="loadFavorites">刷新收藏</el-button>
      <el-button type="danger" @click="clearAllFavorites">清空收藏</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ToolManagement',
  data() {
    return {
      tools: [],
      searchKeyword: '',
      favoriteCount: 0,
      isLoggedIn: false
    }
  },
  
  mounted() {
    this.checkLoginStatus();
    this.loadTools();
    if (this.isLoggedIn) {
      this.loadFavoriteCount();
    }
  },
  
  methods: {
    checkLoginStatus() {
      this.isLoggedIn = !!localStorage.getItem('accessToken');
    },
    
    async loadTools() {
      try {
        const result = await getAllTools();
        if (result.code === 200) {
          this.tools = result.data;
        }
      } catch (error) {
        this.$message.error('加载工具失败');
      }
    },
    
    async handleSearch() {
      if (!this.searchKeyword.trim()) {
        this.loadTools();
        return;
      }
      
      try {
        const result = await searchTools(this.searchKeyword);
        if (result.code === 200) {
          this.tools = result.data;
        }
      } catch (error) {
        this.$message.error('搜索失败');
      }
    },
    
    async toggleFavorite(tool) {
      if (!this.isLoggedIn) {
        this.$message.warning('请先登录');
        return;
      }
      
      try {
        const result = await toggleFavorite(tool.id);
        if (result.code === 200) {
          tool.isFavorited = result.data;
          tool.favoriteCount += result.data ? 1 : -1;
          this.loadFavoriteCount();
          this.$message.success(result.message);
        }
      } catch (error) {
        this.$message.error('操作失败');
      }
    },
    
    async visitTool(tool) {
      // 记录点击
      if (this.isLoggedIn) {
        try {
          await fetch(`${API_BASE}/tools/${tool.id}/click`, {
            method: 'POST',
            headers: getAuthHeaders()
          });
          tool.clickCount++;
        } catch (error) {
          console.error('记录点击失败:', error);
        }
      }
      
      // 打开工具
      window.open(tool.url, '_blank');
    },
    
    async loadFavoriteCount() {
      try {
        const result = await fetch(`${API_BASE}/users/favorites/count`, {
          headers: getAuthHeaders()
        });
        const data = await result.json();
        if (data.code === 200) {
          this.favoriteCount = data.data;
        }
      } catch (error) {
        console.error('加载收藏统计失败:', error);
      }
    },
    
    async clearAllFavorites() {
      try {
        await this.$confirm('确定要清空所有收藏吗？', '确认操作');
        
        const result = await fetch(`${API_BASE}/users/favorites/clear`, {
          method: 'DELETE',
          headers: getAuthHeaders()
        });
        const data = await result.json();
        
        if (data.code === 200) {
          this.$message.success(`已清空 ${data.data} 个收藏`);
          this.loadTools();
          this.loadFavoriteCount();
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('清空失败');
        }
      }
    }
  }
}
</script>
```

## 🔧 数据库设计

### 相关表结构

1. **users表** - 用户信息
2. **tools表** - 工具信息
3. **user_tools表** - 用户工具收藏关联表

### 关键字段说明

**tools表**：
- `click_count`: 工具点击次数
- `status`: 工具状态（ACTIVE/INACTIVE）
- `category_id`: 分类ID

**user_tools表**：
- `user_id`: 用户ID（外键）
- `tool_id`: 工具ID（外键）
- `created_at`: 收藏时间
- 唯一约束：`(user_id, tool_id)`

## 🎯 业务逻辑特点

1. **智能显示**：根据用户登录状态显示不同信息
2. **防重复收藏**：数据库唯一约束防止重复收藏
3. **软删除支持**：工具支持逻辑删除
4. **统计功能**：实时统计点击次数和收藏次数
5. **批量操作**：支持批量收藏和取消收藏
6. **事务保证**：关键操作使用事务保证数据一致性

## 🚀 快速测试

1. **启动项目**：`mvn spring-boot:run`
2. **访问Swagger**：http://localhost:8081/rpal_portal/swagger-ui.html
3. **测试流程**：
   - 注册用户并登录获取Token
   - 使用Token测试收藏功能
   - 不登录测试工具浏览功能

现在您的RPAL Portal Backend已经具备完整的用户工具收藏功能！

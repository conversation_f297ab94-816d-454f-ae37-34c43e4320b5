# CORS跨域问题解决指南

## 🚨 什么是CORS跨域问题？

CORS（Cross-Origin Resource Sharing）跨域资源共享是浏览器的一种安全机制。当前端应用（如React、Vue等）运行在一个域名/端口上，而后端API运行在另一个域名/端口上时，浏览器会阻止这种跨域请求。

### 常见的跨域场景：
- 前端：`http://localhost:3000`
- 后端：`http://localhost:8081`
- 错误信息：`Access to fetch at 'http://localhost:8081/api/users/login' from origin 'http://localhost:3000' has been blocked by CORS policy`

## ✅ 已实施的解决方案

### 1. Spring Security CORS配置

在 `SecurityConfig.java` 中添加了完整的CORS配置：

```java
@Bean
public CorsConfigurationSource corsConfigurationSource() {
    CorsConfiguration configuration = new CorsConfiguration();
    
    // 允许的源（开发环境）
    configuration.setAllowedOriginPatterns(Arrays.asList(
            "http://localhost:*",
            "http://127.0.0.1:*",
            "https://localhost:*",
            "https://127.0.0.1:*"
    ));
    
    // 允许的HTTP方法
    configuration.setAllowedMethods(Arrays.asList(
            "GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"
    ));
    
    // 允许的请求头
    configuration.setAllowedHeaders(Arrays.asList(
            "Authorization", "Content-Type", "X-Requested-With", 
            "Accept", "Origin", "Access-Control-Request-Method", 
            "Access-Control-Request-Headers"
    ));
    
    // 允许携带凭证
    configuration.setAllowCredentials(true);
    
    return source;
}
```

### 2. 全局WebMvc CORS配置

在 `CorsConfig.java` 中提供了额外的CORS配置：

```java
@Bean
public WebMvcConfigurer corsConfigurer() {
    return new WebMvcConfigurer() {
        @Override
        public void addCorsMappings(CorsRegistry registry) {
            registry.addMapping("/**")
                    .allowedOriginPatterns("*")
                    .allowedMethods(allowedMethods)
                    .allowedHeaders(allowedHeaders)
                    .allowCredentials(allowCredentials)
                    .maxAge(maxAge);
        }
    };
}
```

### 3. 配置文件支持

在 `application.yml` 中添加了可配置的CORS参数：

```yaml
cors:
  allowed-origins: http://localhost:3000,http://localhost:8080,http://127.0.0.1:3000,http://127.0.0.1:8080,http://localhost:5173,http://127.0.0.1:5173
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS,PATCH
  allowed-headers: Authorization,Content-Type,X-Requested-With,Accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers
  allow-credentials: true
  max-age: 3600
```

## 🔧 前端配置示例

### React/JavaScript 示例

```javascript
// 基本的fetch请求
const response = await fetch('http://localhost:8081/rpal_portal/users/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token // 如果需要认证
  },
  credentials: 'include', // 重要：允许携带凭证
  body: JSON.stringify({
    emailOrUsername: '<EMAIL>',
    password: 'password123'
  })
});

// Axios配置
import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:8081/rpal_portal',
  withCredentials: true, // 重要：允许携带凭证
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器添加Token
api.interceptors.request.use(config => {
  const token = localStorage.getItem('accessToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

### Vue.js 示例

```javascript
// main.js 或 api.js
import axios from 'axios'

axios.defaults.baseURL = 'http://localhost:8081/rpal_portal'
axios.defaults.withCredentials = true

// 请求拦截器
axios.interceptors.request.use(config => {
  const token = localStorage.getItem('accessToken')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// 组件中使用
export default {
  methods: {
    async login() {
      try {
        const response = await axios.post('/users/login', {
          emailOrUsername: this.email,
          password: this.password
        })
        console.log('登录成功:', response.data)
      } catch (error) {
        console.error('登录失败:', error)
      }
    }
  }
}
```

## 🛠️ 常见问题排查

### 1. 预检请求失败

**问题**：OPTIONS请求返回405或其他错误
**解决**：确保后端支持OPTIONS方法

```java
// 在SecurityConfig中确保OPTIONS请求被允许
.requestMatchers(HttpMethod.OPTIONS, "/**").permitAll()
```

### 2. 凭证携带问题

**问题**：`Access-Control-Allow-Credentials` 错误
**解决**：
- 后端设置 `allowCredentials(true)`
- 前端设置 `credentials: 'include'` 或 `withCredentials: true`
- 不能同时使用 `allowedOrigins("*")` 和 `allowCredentials(true)`

### 3. 自定义请求头被拒绝

**问题**：Authorization头被CORS阻止
**解决**：在 `allowedHeaders` 中明确添加所需的头

### 4. 生产环境配置

**开发环境**（当前配置）：
```java
.allowedOriginPatterns("*") // 允许所有源
```

**生产环境**（推荐配置）：
```java
.allowedOrigins(
    "https://your-frontend-domain.com",
    "https://www.your-frontend-domain.com"
)
```

## 🔍 调试技巧

### 1. 浏览器开发者工具

- 打开 Network 标签
- 查看 OPTIONS 预检请求
- 检查响应头中的 CORS 相关字段：
  - `Access-Control-Allow-Origin`
  - `Access-Control-Allow-Methods`
  - `Access-Control-Allow-Headers`
  - `Access-Control-Allow-Credentials`

### 2. 后端日志

在 `application.yml` 中启用CORS调试：

```yaml
logging:
  level:
    org.springframework.web.cors: DEBUG
    org.springframework.security.web.access: DEBUG
```

### 3. 测试工具

使用Postman或curl测试API（这些工具不受CORS限制）：

```bash
curl -X POST http://localhost:8081/rpal_portal/users/login \
  -H "Content-Type: application/json" \
  -d '{"emailOrUsername":"<EMAIL>","password":"password123"}'
```

## 📋 配置检查清单

- ✅ SecurityConfig中启用CORS：`.cors(cors -> cors.configurationSource(corsConfigurationSource()))`
- ✅ 配置允许的源：开发环境使用通配符，生产环境使用具体域名
- ✅ 配置允许的方法：包含所需的HTTP方法
- ✅ 配置允许的头：包含Authorization、Content-Type等
- ✅ 启用凭证支持：`allowCredentials(true)`
- ✅ 前端正确设置：`credentials: 'include'` 或 `withCredentials: true`

## 🚀 快速验证

重启后端服务后，可以通过以下方式验证CORS配置：

1. **浏览器控制台测试**：
```javascript
fetch('http://localhost:8081/rpal_portal/hello/world', {
  method: 'GET',
  credentials: 'include'
}).then(response => response.text()).then(console.log);
```

2. **检查响应头**：
确保响应包含正确的CORS头信息。

现在您的RPAL Portal Backend已经完全支持跨域请求，可以与任何前端框架无缝集成！
